<!-- Created with Jaspersoft Studio version 7.0.0.final using JasperReports Library version 7.0.0-b478feaa9aab4375eba71de77b4ca138ad2f62aa  -->
<jasperReport name="invoice_velocity_wheels" language="java" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="1c425caa-b5d7-4443-83a8-f5881166789a">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<style name="Table_TH" mode="Opaque" backcolor="#1E3A8A">
		<box>
			<topPen lineWidth="0.5" lineColor="#1E3A8A"/>
			<bottomPen lineWidth="0.5" lineColor="#1E3A8A"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#3B82F6">
		<box>
			<topPen lineWidth="0.5" lineColor="#3B82F6"/>
			<bottomPen lineWidth="0.5" lineColor="#3B82F6"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<topPen lineWidth="0.5" lineColor="#E5E7EB"/>
			<bottomPen lineWidth="0.5" lineColor="#E5E7EB"/>
		</box>
	</style>
	<dataset name="items" uuid="9e3b69bc-026e-46ed-8af6-6595e0177a5e">
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="NO_DATA_ADAPTER"/>
		<query language="EJBQL"><![CDATA[]]></query>
		<field name="description" class="java.lang.String"/>
		<field name="quantity" class="java.math.BigDecimal"/>
		<field name="unitPrice" class="java.math.BigDecimal"/>
		<field name="totalPrice" class="java.math.BigDecimal"/>
		<field name="taxRate" class="java.math.BigDecimal"/>
	</dataset>
	<parameter name="itemDataSource" class="net.sf.jasperreports.engine.data.JRBeanCollectionDataSource"/>
	<parameter name="merchantAddress" class="java.lang.String"/>
	<parameter name="billingAddress" class="java.lang.String"/>
	<parameter name="logo" class="java.lang.Object"/>
	<parameter name="header" class="java.lang.String"/>
	<parameter name="companyName" class="java.lang.String"/>
	<parameter name="companyPhone" class="java.lang.String"/>
	<parameter name="companyEmail" class="java.lang.String"/>
	<parameter name="customerName" class="java.lang.String"/>
	<parameter name="bankAccount" class="java.lang.String"/>
	<parameter name="bankName" class="java.lang.String"/>
	<parameter name="bic" class="java.lang.String"/>
	<parameter name="netTotal" class="java.math.BigDecimal"/>
	<parameter name="taxTotal" class="java.math.BigDecimal"/>
	<parameter name="grossTotal" class="java.math.BigDecimal"/>
	<parameter name="epcQrCode" class="java.lang.Object"/>
	<parameter name="HEADER_DEPOSIT" class="java.lang.String"/>
	<parameter name="HEADER_FINAL" class="java.lang.String"/>
	<parameter name="HEADER_REVERSED" class="java.lang.String"/>
	<parameter name="CUSTOMER_LABEL" class="java.lang.String"/>
	<parameter name="DATE_LABEL" class="java.lang.String"/>
	<parameter name="ORDER_LABEL" class="java.lang.String"/>
	<parameter name="DESCRIPTION_LABEL" class="java.lang.String"/>
	<parameter name="QUANTITY_LABEL" class="java.lang.String"/>
	<parameter name="UNIT_PRICE_LABEL" class="java.lang.String"/>
	<parameter name="TOTAL_PRICE_LABEL" class="java.lang.String"/>
	<parameter name="NET_TOTAL_LABEL" class="java.lang.String"/>
	<parameter name="TAX_LABEL" class="java.lang.String"/>
	<parameter name="GROSS_TOTAL_LABEL" class="java.lang.String"/>
	<parameter name="PAYMENT_INFO_LABEL" class="java.lang.String"/>
	<parameter name="THANK_YOU_LABEL" class="java.lang.String"/>
	<query language="sql"><![CDATA[]]></query>
	<detail>
		<band height="400" splitType="Stretch">
			<!-- Company Logo -->
			<element kind="image" uuid="d59e0d3b-430a-4256-9665-ae9d391b8138" x="453" y="10" width="100" height="100">
				<expression><![CDATA[$P{logo}]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
			</element>
			
			<!-- Company Information -->
			<element kind="textField" uuid="company-name" x="15" y="10" width="300" height="25" fontName="DejaVu Sans" fontSize="18.0" pdfFontName="Helvetica-Bold" pdfEncoding="Cp1252" pdfEmbedded="true" bold="true" forecolor="#1E3A8A">
				<expression><![CDATA[$P{companyName}]]></expression>
			</element>
			<element kind="textField" uuid="company-phone" x="15" y="35" width="200" height="15" fontName="DejaVu Sans" fontSize="10.0" pdfFontName="Helvetica" pdfEncoding="Cp1252" pdfEmbedded="true">
				<expression><![CDATA["Phone: " + $P{companyPhone}]]></expression>
			</element>
			<element kind="textField" uuid="company-email" x="15" y="50" width="250" height="15" fontName="DejaVu Sans" fontSize="10.0" pdfFontName="Helvetica" pdfEncoding="Cp1252" pdfEmbedded="true">
				<expression><![CDATA["Email: " + $P{companyEmail}]]></expression>
			</element>
			
			<!-- Merchant Address -->
			<element kind="textField" uuid="merchant-address" x="15" y="75" width="305" height="40" fontName="DejaVu Sans" fontSize="10.0" pdfFontName="Helvetica" pdfEncoding="Cp1252" pdfEmbedded="true">
				<expression><![CDATA[$P{merchantAddress}]]></expression>
			</element>
			
			<!-- Billing Address -->
			<element kind="textField" uuid="billing-address" x="15" y="130" width="240" height="70" fontName="DejaVu Sans" fontSize="10.0" pdfFontName="Helvetica" pdfEncoding="Cp1252" pdfEmbedded="true">
				<expression><![CDATA[$P{billingAddress}]]></expression>
			</element>
			
			<!-- Header -->
			<element kind="textField" uuid="header" x="14" y="220" width="400" height="30" fontName="DejaVu Sans" fontSize="16.0" pdfFontName="Helvetica-Bold" pdfEncoding="Cp1252" pdfEmbedded="true" bold="true" forecolor="#1E3A8A">
				<expression><![CDATA[$P{header}]]></expression>
			</element>
			
			<!-- Items Table -->
			<element kind="component" uuid="77cfd0e7-a339-4545-a8a2-5aab442a7a45" positionType="Float" x="20" y="270" width="533" height="60">
				<component kind="table" whenNoDataType="AllSectionsNoDetail">
					<datasetRun uuid="ed6cd1bf-e2a8-4fa3-bc26-11db035c5b9b" subDataset="items">
						<dataSourceExpression><![CDATA[$P{itemDataSource}]]></dataSourceExpression>
					</datasetRun>
					<column kind="single" uuid="207229b2-dc8a-451d-a77e-6dfdd4e7c85c" width="220">
						<tableHeader height="30" rowSpan="1" style="Table_TH">
							<element kind="textField" uuid="c080b0a3-51b9-4c9c-9cf3-4a446610bf80" x="0" y="0" width="220" height="30" fontName="DejaVu Sans" fontSize="11.0" pdfFontName="Helvetica-Bold" pdfEncoding="Cp1252" pdfEmbedded="true" bold="true" hTextAlign="Left" vTextAlign="Middle" forecolor="#FFFFFF">
								<paragraph leftIndent="10"/>
								<expression><![CDATA[$P{DESCRIPTION_LABEL}]]></expression>
							</element>
						</tableHeader>
						<detailCell height="25" style="Table_TD">
							<element kind="textField" uuid="51f5e9f0-b282-4b52-8fbe-f9941a1bce67" x="0" y="0" width="220" height="25" fontName="DejaVu Sans" fontSize="10.0" pdfFontName="Helvetica" pdfEncoding="Cp1252" pdfEmbedded="true" hTextAlign="Left" vTextAlign="Middle">
								<paragraph leftIndent="10"/>
								<expression><![CDATA[$F{description}]]></expression>
							</element>
						</detailCell>
					</column>
					<column kind="single" uuid="2866f326-de4b-471a-b310-e78434f88c96" width="80">
						<tableHeader height="30" rowSpan="1" style="Table_TH">
							<element kind="textField" uuid="1f128c6d-5ceb-4014-858e-e7307c8642b8" x="0" y="0" width="80" height="30" fontName="DejaVu Sans" fontSize="11.0" pdfFontName="Helvetica-Bold" pdfEncoding="Cp1252" pdfEmbedded="true" bold="true" hTextAlign="Center" vTextAlign="Middle" forecolor="#FFFFFF">
								<expression><![CDATA[$P{QUANTITY_LABEL}]]></expression>
							</element>
						</tableHeader>
						<detailCell height="25" style="Table_TD">
							<element kind="textField" uuid="343cca4a-1c12-416d-81d4-56067fc307c9" x="0" y="0" width="80" height="25" fontName="DejaVu Sans" fontSize="10.0" pdfFontName="Helvetica" pdfEncoding="Cp1252" pdfEmbedded="true" hTextAlign="Center" vTextAlign="Middle">
								<expression><![CDATA[$F{quantity}]]></expression>
							</element>
						</detailCell>
					</column>
					<column kind="single" uuid="7f95dda6-1ceb-4181-bf18-cd544853672d" width="115">
						<tableHeader height="30" rowSpan="1" style="Table_TH">
							<element kind="textField" uuid="ecfbbe5b-4803-4e7d-9ddb-896de1029a1e" x="0" y="0" width="115" height="30" fontName="DejaVu Sans" fontSize="11.0" pdfFontName="Helvetica-Bold" pdfEncoding="Cp1252" pdfEmbedded="true" bold="true" hTextAlign="Right" vTextAlign="Middle" forecolor="#FFFFFF">
								<paragraph rightIndent="10"/>
								<expression><![CDATA[$P{UNIT_PRICE_LABEL}]]></expression>
							</element>
						</tableHeader>
						<detailCell height="25" style="Table_TD">
							<element kind="textField" uuid="a8e029d3-d207-4899-a06d-9ea9096c0ce8" x="0" y="0" width="115" height="25" fontName="DejaVu Sans" fontSize="10.0" pdfFontName="Helvetica" pdfEncoding="Cp1252" pdfEmbedded="true" hTextAlign="Right" vTextAlign="Middle">
								<paragraph rightIndent="10"/>
								<expression><![CDATA[$F{unitPrice}]]></expression>
							</element>
						</detailCell>
					</column>
					<column kind="single" uuid="46af2fde-8044-4bab-8148-93361a84b14c" width="118">
						<tableHeader height="30" rowSpan="1" style="Table_TH">
							<element kind="textField" uuid="8877e3c6-ba08-4307-9331-14c4f35862f2" x="0" y="0" width="118" height="30" fontName="DejaVu Sans" fontSize="11.0" pdfFontName="Helvetica-Bold" pdfEncoding="Cp1252" pdfEmbedded="true" bold="true" hTextAlign="Right" vTextAlign="Middle" forecolor="#FFFFFF">
								<paragraph rightIndent="10"/>
								<expression><![CDATA[$P{TOTAL_PRICE_LABEL}]]></expression>
							</element>
						</tableHeader>
						<detailCell height="25" style="Table_TD">
							<element kind="textField" uuid="ba6391ed-638a-4cd1-bbc9-da1dfe4d989f" x="0" y="0" width="118" height="25" fontName="DejaVu Sans" fontSize="10.0" pdfFontName="Helvetica" pdfEncoding="Cp1252" pdfEmbedded="true" hTextAlign="Right" vTextAlign="Middle">
								<paragraph rightIndent="10"/>
								<expression><![CDATA[$F{totalPrice}]]></expression>
							</element>
						</detailCell>
					</column>
				</component>
			</element>
		</band>
		
		<!-- Totals Section -->
		<band height="80" splitType="Immediate">
			<element kind="textField" uuid="net-total-label" x="350" y="15" width="120" height="15" fontName="DejaVu Sans" fontSize="11.0" pdfFontName="Helvetica" pdfEncoding="Cp1252" pdfEmbedded="true" hTextAlign="Right">
				<expression><![CDATA[$P{NET_TOTAL_LABEL}]]></expression>
			</element>
			<element kind="textField" uuid="net-total" x="475" y="15" width="80" height="15" fontName="DejaVu Sans" fontSize="11.0" pdfFontName="Helvetica" pdfEncoding="Cp1252" pdfEmbedded="true" hTextAlign="Right">
				<expression><![CDATA[$P{netTotal}]]></expression>
			</element>
			
			<element kind="textField" uuid="tax-label" x="350" y="35" width="120" height="15" fontName="DejaVu Sans" fontSize="11.0" pdfFontName="Helvetica" pdfEncoding="Cp1252" pdfEmbedded="true" hTextAlign="Right">
				<expression><![CDATA[$P{TAX_LABEL}]]></expression>
			</element>
			<element kind="textField" uuid="tax-total" x="475" y="35" width="80" height="15" fontName="DejaVu Sans" fontSize="11.0" pdfFontName="Helvetica" pdfEncoding="Cp1252" pdfEmbedded="true" hTextAlign="Right">
				<expression><![CDATA[$P{taxTotal}]]></expression>
			</element>
			
			<element kind="textField" uuid="gross-total-label" x="350" y="55" width="120" height="15" fontName="DejaVu Sans" fontSize="12.0" pdfFontName="Helvetica-Bold" pdfEncoding="Cp1252" pdfEmbedded="true" bold="true" hTextAlign="Right">
				<expression><![CDATA[$P{GROSS_TOTAL_LABEL}]]></expression>
			</element>
			<element kind="textField" uuid="gross-total" x="475" y="55" width="80" height="15" fontName="DejaVu Sans" fontSize="12.0" pdfFontName="Helvetica-Bold" pdfEncoding="Cp1252" pdfEmbedded="true" bold="true" hTextAlign="Right">
				<expression><![CDATA[$P{grossTotal}]]></expression>
			</element>
		</band>
		
		<!-- Payment Information and QR Code -->
		<band height="120" splitType="Immediate">
			<element kind="textField" uuid="payment-info-label" x="15" y="10" width="200" height="20" fontName="DejaVu Sans" fontSize="12.0" pdfFontName="Helvetica-Bold" pdfEncoding="Cp1252" pdfEmbedded="true" bold="true" forecolor="#1E3A8A">
				<expression><![CDATA[$P{PAYMENT_INFO_LABEL}]]></expression>
			</element>
			
			<element kind="textField" uuid="bank-name" x="15" y="35" width="300" height="15" fontName="DejaVu Sans" fontSize="10.0" pdfFontName="Helvetica" pdfEncoding="Cp1252" pdfEmbedded="true">
				<expression><![CDATA["Bank: " + $P{bankName}]]></expression>
			</element>
			<element kind="textField" uuid="bank-account" x="15" y="50" width="300" height="15" fontName="DejaVu Sans" fontSize="10.0" pdfFontName="Helvetica" pdfEncoding="Cp1252" pdfEmbedded="true">
				<expression><![CDATA["Account: " + $P{bankAccount}]]></expression>
			</element>
			<element kind="textField" uuid="bic" x="15" y="65" width="300" height="15" fontName="DejaVu Sans" fontSize="10.0" pdfFontName="Helvetica" pdfEncoding="Cp1252" pdfEmbedded="true">
				<expression><![CDATA["BIC: " + $P{bic}]]></expression>
			</element>
			
			<!-- EPC QR Code -->
			<element kind="image" uuid="6daf72cb-21c1-4cea-a9c3-ed9ebdcda313" x="450" y="10" width="80" height="80" scaleImage="FillFrame" hImageAlign="Center" vImageAlign="Middle">
				<expression><![CDATA[$P{epcQrCode}]]></expression>
			</element>
			
			<element kind="textField" uuid="thank-you" x="15" y="90" width="400" height="20" fontName="DejaVu Sans" fontSize="11.0" pdfFontName="Helvetica" pdfEncoding="Cp1252" pdfEmbedded="true" forecolor="#1E3A8A">
				<expression><![CDATA[$P{THANK_YOU_LABEL}]]></expression>
			</element>
		</band>
	</detail>
	
	<pageFooter height="50">
		<element kind="textField" uuid="page-number" x="220" y="20" width="114" height="20" forecolor="#9CA3AF" fontName="DejaVu Sans" fontSize="10.0" pdfFontName="Helvetica" pdfEncoding="Cp1252" pdfEmbedded="true" hTextAlign="Center" vTextAlign="Middle">
			<expression><![CDATA["Page " + $V{PAGE_NUMBER}]]></expression>
		</element>
		<element kind="textField" uuid="company-footer" x="15" y="20" width="200" height="20" forecolor="#9CA3AF" fontName="DejaVu Sans" fontSize="9.0" pdfFontName="Helvetica" pdfEncoding="Cp1252" pdfEmbedded="true">
			<expression><![CDATA[$P{companyName}]]></expression>
		</element>
	</pageFooter>
</jasperReport>
